# Hook.go 测试文档

## 概述

本文档描述了为 `hook.go` 文件创建的全面测试套件。测试文件 `hook_test.go` 包含了多种测试场景，确保代码的可靠性和正确性。

## 测试覆盖范围

### 1. 参数验证测试 (`TestProcessFileMove_ArgumentValidation`)
- 测试参数数量不足的情况（少于3个参数）
- 测试 `file_count` 为空字符串的情况
- 测试 `first_file_path` 为空字符串的情况
- 测试 `file_count` 不是有效数字的情况
- 测试 `file_count` 小于等于0的情况

### 2. 文件路径解析测试 (`TestProcessFileMove_PathParsing`)
- 测试正常的文件路径解析（路径包含至少4个"/"分隔符）
- 测试路径格式不正确的情况（路径分隔符少于4个）

### 3. 文件操作测试 (`TestProcessFileMove_FileOperations`)
- 测试源文件存在时的移动操作
- 测试源文件不存在时的错误处理
- 验证文件是否正确移动到目标位置

### 4. 重命名失败测试 (`TestProcessFileMove_RenameFailure`)
- 测试 `os.Rename` 失败时回退到 `MoveFileExec` 的情况
- 模拟权限问题导致的重命名失败

### 5. MoveFileExec 函数测试 (`TestMoveFileExec`)
- 测试 `mv` 命令的成功执行
- 测试命令执行失败的情况

### 6. 日志输出测试 (`TestLogOutput`)
- 验证所有日志消息都正确输出（使用 `log` 包而不是 `fmt`）
- 确保日志包含时间戳

### 7. Chmod 失败测试 (`TestChmodFailure`)
- 测试 `chmod` 操作失败的情况

### 8. 集成测试 (`TestIntegration`)
- 端到端测试完整的文件处理流程
- 验证文件内容的完整性

## 运行测试

### 运行所有测试
```bash
go test -v hook_test.go hook.go
```

### 运行测试并显示覆盖率
```bash
go test -cover hook_test.go hook.go
```

### 生成详细的覆盖率报告
```bash
go test -coverprofile=coverage.out hook_test.go hook.go
go tool cover -func=coverage.out
```

### 生成 HTML 覆盖率报告
```bash
go test -coverprofile=coverage.out hook_test.go hook.go
go tool cover -html=coverage.out -o coverage.html
```

## 测试特性

### Table-Driven Tests
测试使用了 table-driven 模式，特别是在参数验证和路径解析测试中，这使得添加新的测试用例变得简单。

### 临时文件和目录
测试使用临时文件和目录来确保：
- 测试之间相互独立
- 不依赖外部文件系统状态
- 测试后自动清理

### 日志捕获
测试包含了日志输出捕获功能，可以验证：
- 日志消息的内容
- 日志格式的正确性
- 时间戳的存在

### 错误模拟
测试模拟了各种错误情况：
- 文件不存在
- 权限问题
- 命令执行失败

## 测试覆盖率

当前测试覆盖率：**84.8%**

- `ProcessFileMove` 函数：94.5%
- `MoveFileExec` 函数：100%
- `main` 函数：0%（正常，因为 main 函数通常不进行单元测试）

## CI/CD 兼容性

测试设计为在 CI/CD 环境中稳定运行：
- 使用临时目录避免文件系统冲突
- 不依赖特定的系统配置
- 包含适当的清理机制
- 使用确定性的测试数据

## 注意事项

1. 测试需要在支持 `mv` 和 `chmod` 命令的系统上运行
2. 某些测试可能需要文件系统权限来创建和修改文件
3. 测试会创建临时文件和目录，但会在测试完成后自动清理
