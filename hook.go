package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

// ProcessFileMove 处理文件移动的主要逻辑
func ProcessFileMove(file_count_num int, first_file_path string, sourceFolder, targetFolder string) error {

	// 要移动的文件路径
	var source_file string
	var target_file string

	// 判断下载的文件是文件夹还是单个文件
	file_paths := strings.Split(first_file_path, "/")
	if len(file_paths) >= 4 {
		log.Printf("file_paths has %d /\n", len(file_paths))
		source_file = sourceFolder + file_paths[3]
		target_file = targetFolder + file_paths[3]
		log.Println("source_file: " + source_file)
	} else {
		log.Println("first_file_path not right, return")
		return nil
	}

	if source_file != "" {
		log.Println("start move form " + source_file + " to " + target_file)
		_, err := os.Stat(source_file)
		if err != nil {
			log.Println("source_file:"+source_file+"not exist", err)
			return err
		}
		err = os.Rename(source_file, target_file)
		if err != nil {
			log.Println("move error, try copy", err)

			// 复制文件到目标文件夹中
			err := MoveFileExec(source_file, target_file)
			if err != nil {
				log.Println("move file error", err)
				return err
			}
		}
		log.Println("move finish")
		log.Println("chomd start")
		cmd := exec.Command("chmod", "-R", "o+w", target_file)
		err = cmd.Run()
		if nil != err {
			log.Println("chmod fail", err)
			return err
		}
		log.Println("chmod finish")
	} else {
		log.Println(source_file + " is empty")
	}

	return nil
}

// parseAndValidateArgs 解析并验证命令行参数
func parseAndValidateArgs(args []string) (gid string, fileCount int, firstFilePath string, err error) {
	if len(args) <= 2 {
		return "", 0, "", fmt.Errorf("args is less than 2: %d, not file download", len(args))
	}
	gid = args[0]
	file_count := args[1]
	first_file_path := args[2]

	if first_file_path == "" {
		return gid, 0, first_file_path, fmt.Errorf("first_file_path is empty, return")
	}
	if file_count == "" {
		return gid, 0, first_file_path, fmt.Errorf("file_count is empty, return")
	}
	file_count_num, convErr := strconv.Atoi(file_count)
	if convErr != nil {
		return gid, 0, first_file_path, fmt.Errorf("file_count is not i:")
	}
	if file_count_num <= 0 {
		return gid, file_count_num, first_file_path, fmt.Errorf("file_count_num <= 0, retrun")
	}
	return gid, file_count_num, first_file_path, nil
}

// 在aria2c下载完成的时候调用这个脚本
// 将下载的文件从 ssd 移动到 hdd
func main() {
	// 接受3个参数：GID,file_counts,first_file_path
	// gid := flag.Args()[0]
	flag.Parse()

	// 源文件夹
	source_folder := "/aria2/ssd/"
	// 目标文件夹
	target_folder := "/aria2/hdd/"

	_, file_count_num, first_file_path, err := parseAndValidateArgs(flag.Args())
	if err != nil {
		log.Println(err.Error())
		os.Exit(1)
	}
	log.Println("file_count: " + strconv.Itoa(file_count_num))
	log.Println("first_file_path: " + first_file_path)
	log.Printf("file_count_num is %d\n", file_count_num)

	err = ProcessFileMove(file_count_num, first_file_path, source_folder, target_folder)
	if err != nil {
		log.Printf("Error processing file move: %v", err)
		os.Exit(1)
	}
}

func MoveFileExec(source_file string, target_file string) error {
	// source_file_Path := strconv.Quote(source_file)
	// target_file_Path := strconv.Quote(target_file)
	log.Println("run command: mv " + source_file + " " + target_file)
	cmd := exec.Command("mv", source_file, target_file)
	err := cmd.Run()
	return err
}
