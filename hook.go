package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

var db *sql.DB

// 初始化数据库并创建表
func InitDB(dbPath string) (*sql.DB, error) {
	database, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}
	createTableSQL := `CREATE TABLE IF NOT EXISTS file_operations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		gid TEXT,
		file_count_num INTEGER,
		source_file TEXT,
		target_file TEXT,
		timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
		status TEXT,
		error_message TEXT
	);`
	_, err = database.Exec(createTableSQL)
	if err != nil {
		return nil, err
	}
	return database, nil
}

// 插入操作记录
func InsertFileOperation(gid string, fileCount int, sourceFile, targetFile, status, errorMsg string) error {
	if db == nil {
		return fmt.Errorf("db is not initialized")
	}
	stmt := `INSERT INTO file_operations (gid, file_count_num, source_file, target_file, status, error_message) VALUES (?, ?, ?, ?, ?, ?)`
	_, err := db.Exec(stmt, gid, fileCount, sourceFile, targetFile, status, errorMsg)
	return err
}

// ProcessFileMove 处理文件移动的主要逻辑
func ProcessFileMove(file_count_num int, first_file_path string, sourceFolder, targetFolder string) error {
	// 要移动的文件路径
	var source_file string
	var target_file string

	// 判断下载的文件是文件夹还是单个文件
	file_paths := strings.Split(first_file_path, "/")
	if len(file_paths) >= 4 {
		log.Printf("file_paths has %d /\n", len(file_paths))
		source_file = sourceFolder + file_paths[3]
		target_file = targetFolder + file_paths[3]
		log.Println("source_file: " + source_file)
	} else {
		log.Println("first_file_path not right, return")
		return nil
	}

	var opStatus = "success"
	var opErrMsg = ""

	if source_file != "" {
		log.Println("start move form " + source_file + " to " + target_file)
		_, err := os.Stat(source_file)
		if err != nil {
			log.Println("source_file:"+source_file+"not exist", err)
			opStatus = "failed"
			opErrMsg = err.Error()
			InsertFileOperation("", file_count_num, source_file, target_file, opStatus, opErrMsg)
			return err
		}
		err = os.Rename(source_file, target_file)
		if err != nil {
			log.Println("move error, try copy", err)
			// 复制文件到目标文件夹中
			err := MoveFileExec(source_file, target_file)
			if err != nil {
				log.Println("move file error", err)
				opStatus = "failed"
				opErrMsg = err.Error()
				InsertFileOperation("", file_count_num, source_file, target_file, opStatus, opErrMsg)
				return err
			}
		}
		log.Println("move finish")
		log.Println("chomd start")
		cmd := exec.Command("chmod", "-R", "o+w", target_file)
		err = cmd.Run()
		if nil != err {
			log.Println("chmod fail", err)
			opStatus = "failed"
			opErrMsg = err.Error()
			InsertFileOperation("", file_count_num, source_file, target_file, opStatus, opErrMsg)
			return err
		}
		log.Println("chmod finish")
	} else {
		log.Println(source_file + " is empty")
	}

	InsertFileOperation("", file_count_num, source_file, target_file, opStatus, opErrMsg)
	return nil
}

// parseAndValidateArgs 解析并验证命令行参数
func parseAndValidateArgs(args []string) (gid string, fileCount int, firstFilePath string, err error) {
	if len(args) <= 2 {
		return "", 0, "", fmt.Errorf("args is less than 2: %d, not file download", len(args))
	}
	gid = args[0]
	file_count := args[1]
	first_file_path := args[2]

	if first_file_path == "" {
		return gid, 0, first_file_path, fmt.Errorf("first_file_path is empty, return")
	}
	if file_count == "" {
		return gid, 0, first_file_path, fmt.Errorf("file_count is empty, return")
	}
	file_count_num, convErr := strconv.Atoi(file_count)
	if convErr != nil {
		return gid, 0, first_file_path, fmt.Errorf("file_count is not i:")
	}
	if file_count_num <= 0 {
		return gid, file_count_num, first_file_path, fmt.Errorf("file_count_num <= 0, retrun")
	}
	return gid, file_count_num, first_file_path, nil
}

// 在aria2c下载完成的时候调用这个脚本
// 将下载的文件从 ssd 移动到 hdd
func main() {
	flag.Parse()

	// 数据库文件路径，可通过环境变量或默认
	dbPath := os.Getenv("ARIA2_HOOK_DB")
	if dbPath == "" {
		dbPath = "aria2_hook.db"
	}

	var err error
	db, err = InitDB(dbPath)
	if err != nil {
		log.Fatalf("failed to init db: %v", err)
	}
	defer db.Close()

	// 源文件夹
	source_folder := "/aria2/ssd/"
	// 目标文件夹
	target_folder := "/aria2/hdd/"

	gid, file_count_num, first_file_path, err := parseAndValidateArgs(flag.Args())
	if err != nil {
		log.Println(err.Error())
		InsertFileOperation(gid, file_count_num, "", "", "failed", err.Error())
		os.Exit(1)
	}
	log.Println("file_count: " + strconv.Itoa(file_count_num))
	log.Println("first_file_path: " + first_file_path)
	log.Printf("file_count_num is %d\n", file_count_num)

	err = ProcessFileMove(file_count_num, first_file_path, source_folder, target_folder)
	if err != nil {
		log.Printf("Error processing file move: %v", err)
		os.Exit(1)
	}
}

func MoveFileExec(source_file string, target_file string) error {
	// source_file_Path := strconv.Quote(source_file)
	// target_file_Path := strconv.Quote(target_file)
	log.Println("run command: mv " + source_file + " " + target_file)
	cmd := exec.Command("mv", source_file, target_file)
	err := cmd.Run()
	return err
}
