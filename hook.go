package main

import (
	"flag"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

// ProcessFileMove 处理文件移动的主要逻辑
func ProcessFileMove(args []string, sourceFolder, targetFolder string) error {
	if len(args) <= 2 {
		log.Println("args is less than 2:" + string(rune(len(args))) + ", not file download ")
		return nil
	}
	file_count := args[1]
	first_file_path := args[2]

	log.Println("file_count: " + file_count)
	log.Println("first_file_path: " + first_file_path)

	// 文件路径判空
	if first_file_path == "" {
		log.Println("first_file_path is empty, return")
		return nil
	}

	// 判断文件数是否为零
	if file_count == "" {
		log.Println("file_count is empty, return")
		return nil
	}

	file_count_num, err := strconv.Atoi(file_count)
	if err != nil {
		log.Println("file_count is not i:", err)
		return nil
	}
	if file_count_num <= 0 {
		log.Println("file_count_num <= 0, retrun")
		return nil
	}
	log.Printf("file_count_num is %d\n", file_count_num)

	// 要移动的文件路径
	var source_file string
	var target_file string

	// 判断下载的文件是文件夹还是单个文件
	log.Printf("file_count_num[%d] is not 1, find up folder\n", file_count_num)
	file_paths := strings.Split(first_file_path, "/")
	if len(file_paths) >= 4 {
		log.Printf("file_paths has %d /\n", len(file_paths))
		source_file = sourceFolder + file_paths[3]
		target_file = targetFolder + file_paths[3]
		log.Println("source_file: " + source_file)
	} else {
		log.Println("first_file_path not right, return")
		return nil
	}

	if source_file != "" {
		log.Println("start move form " + source_file + " to " + target_file)
		_, err := os.Stat(source_file)
		if err != nil {
			log.Println("source_file:"+source_file+"not exist", err)
			return err
		}
		err = os.Rename(source_file, target_file)
		if err != nil {
			log.Println("move error, try copy", err)

			// 复制文件到目标文件夹中
			err := MoveFileExec(source_file, target_file)
			if err != nil {
				log.Println("move file error", err)
				return err
			}
		}
		log.Println("move finish")
		log.Println("chomd start")
		cmd := exec.Command("chmod", "-R", "o+w", target_file)
		err = cmd.Run()
		if nil != err {
			log.Println("chmod fail", err)
			return err
		}
		log.Println("chmod finish")
	} else {
		log.Println(source_file + " is empty")
	}

	return nil
}

// 在aria2c下载完成的时候调用这个脚本
// 将下载的文件从 ssd 移动到 hdd
func main() {
	// 接受3个参数：GID,file_counts,first_file_path
	// gid := flag.Args()[0]
	flag.Parse()

	// 源文件夹
	source_folder := "/aria2/ssd/"
	// 目标文件夹
	target_folder := "/aria2/hdd/"

	err := ProcessFileMove(flag.Args(), source_folder, target_folder)
	if err != nil {
		log.Printf("Error processing file move: %v", err)
		os.Exit(1)
	}
}

func MoveFileExec(source_file string, target_file string) error {
	// source_file_Path := strconv.Quote(source_file)
	// target_file_Path := strconv.Quote(target_file)
	log.Println("run command: mv " + source_file + " " + target_file)
	cmd := exec.Command("mv", source_file, target_file)
	err := cmd.Run()
	return err
}
