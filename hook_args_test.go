package main

import (
	"strings"
	"testing"
)

func TestParseAndValidateArgs(t *testing.T) {
	tests := []struct {
		name           string
		args           []string
		wantGid        string
		wantFileCount  int
		wantFirstPath  string
		wantErr        bool
		errMsgContains string
	}{
		{
			name:           "参数数量不足_0个参数",
			args:           []string{},
			wantErr:        true,
			errMsgContains: "args is less than 2",
		},
		{
			name:           "参数数量不足_1个参数",
			args:           []string{"gid"},
			wantErr:        true,
			errMsgContains: "args is less than 2",
		},
		{
			name:           "参数数量不足_2个参数",
			args:           []string{"gid", "count"},
			wantErr:        true,
			errMsgContains: "args is less than 2",
		},
		{
			name:           "file_count为空字符串",
			args:           []string{"gid", "", "/aria2/ssd/test/file.txt"},
			wantErr:        true,
			errMsgContains: "file_count is empty",
		},
		{
			name:           "first_file_path为空字符串",
			args:           []string{"gid", "1", ""},
			wantErr:        true,
			errMsgContains: "first_file_path is empty",
		},
		{
			name:           "file_count不是有效数字",
			args:           []string{"gid", "abc", "/aria2/ssd/test/file.txt"},
			wantErr:        true,
			errMsgContains: "file_count is not int:",
		},
		{
			name:           "file_count为0",
			args:           []string{"gid", "0", "/aria2/ssd/test/file.txt"},
			wantErr:        true,
			errMsgContains: "file_count_num <= 0",
		},
		{
			name:           "file_count为负数",
			args:           []string{"gid", "-1", "/aria2/ssd/test/file.txt"},
			wantErr:        true,
			errMsgContains: "file_count_num <= 0",
		},
		{
			name:          "正常的有效参数",
			args:          []string{"gid123", "2", "/aria2/ssd/ok.txt"},
			wantGid:       "gid123",
			wantFileCount: 2,
			wantFirstPath: "/aria2/ssd/ok.txt",
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gid, fileCount, firstPath, err := parseAndValidateArgs(tt.args)
			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error but got nil")
				} else if !strings.Contains(err.Error(), tt.errMsgContains) {
					t.Errorf("Expected error message to contain '%s', got '%s'", tt.errMsgContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if gid != tt.wantGid {
					t.Errorf("gid: want %q, got %q", tt.wantGid, gid)
				}
				if fileCount != tt.wantFileCount {
					t.Errorf("fileCount: want %d, got %d", tt.wantFileCount, fileCount)
				}
				if firstPath != tt.wantFirstPath {
					t.Errorf("firstFilePath: want %q, got %q", tt.wantFirstPath, firstPath)
				}
			}
		})
	}
}
