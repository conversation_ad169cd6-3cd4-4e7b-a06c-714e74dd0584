# Aria2 Docker 下载器

基于 Docker 的 [Aria2](https://github.com/aria2/aria2) 下载器，集成 [YAAW](https://github.com/binux/yaaw) Web 界面和 [Caddy](https://caddyserver.com/) 反向代理。

## 快速开始

最简单的运行方式：

```bash
docker run -d --name aria2-docker -p 8080:8080 aria2-docker
```

完整配置运行：

```bash
docker run -d \
    --name aria2-docker \
    -p 8080:8080 \
    -v /path/to/downloads:/aria2/data \    # 下载文件存储目录
    -v /path/to/config:/aria2/conf \       # Aria2 配置文件目录
    -e PUID=1000 \                         # 运行用户的 UID，用于文件权限管理
    -e PGID=1000 \                         # 运行用户的 GID，用于文件权限管理
    aria2-docker
```

## 使用说明

1. 启动容器后，访问 `http://localhost:8080` 打开 YAAW Web 界面
2. 在界面中可以添加下载任务，支持：
   - HTTP/HTTPS 链接
   - FTP 链接
   - BitTorrent 种子文件
   - Magnet 磁力链接
3. 下载的文件将保存在 `/aria2/data` 目录中
4. 可以通过修改 `/aria2/conf/aria2.conf` 文件来自定义 Aria2 配置

## 相关项目

- [Aria2](https://github.com/aria2/aria2) - 多协议下载工具
- [YAAW](https://github.com/binux/yaaw) - Aria2 Web 界面
- [Caddy](https://caddyserver.com/) - 现代化 Web 服务器
