# Aria2 Docker 下载器

基于 Docker 的 [Aria2](https://github.com/aria2/aria2) 下载器，集成 [YAAW](https://github.com/binux/yaaw) Web 界面和 [Caddy](https://caddyserver.com/) 反向代理。

## 项目简介

这是一个轻量级的 Aria2 Docker 容器，提供了完整的下载解决方案：

- **Aria2**: 强大的多协议下载工具，支持 HTTP/HTTPS、FTP、SFTP、BitTorrent 和 Metalink
- **YAAW**: 简洁易用的 Web 管理界面
- **Caddy**: 现代化的 Web 服务器，提供反向代理功能
- **Alpine Linux**: 基于轻量级 Alpine Linux，镜像体积小

## 目录

- [功能特性](#功能特性)
- [快速开始](#快速开始)
  - [Docker 运行](#docker-运行)
  - [Docker Compose](#docker-compose)
  - [Nginx 代理](#nginx-代理配置)
- [配置说明](#配置说明)
  - [环境变量](#环境变量)
  - [数据卷](#数据卷)
  - [用户权限](#用户权限)
- [构建镜像](#构建镜像)

## 功能特性

- 🐳 **容器化部署**: 基于 Docker，部署简单，环境隔离
- 🌐 **Web 管理界面**: 集成 YAAW，提供直观的下载管理
- ⚡ **高性能代理**: 使用 Caddy 提供高效的反向代理服务
- 🔧 **灵活配置**: 支持环境变量配置，适应不同使用场景
- 👥 **用户权限管理**: 支持 PUID/PGID 映射，解决文件权限问题
- 📱 **多平台支持**: 支持 amd64、arm64、arm32 等多种架构

## 快速开始

### Docker 运行

最简单的运行方式：

```bash
docker run -d --name aria2-docker -p 8080:8080 aria2-docker
```

完整配置运行：

```bash
docker run -d \
    --name aria2-docker \
    -p 8080:8080 \
    -v /path/to/downloads:/aria2/data \
    -v /path/to/config:/aria2/conf \
    -e PUID=1000 \
    -e PGID=1000 \
    aria2-docker
```

### Docker Compose

创建 `docker-compose.yml` 文件：

```yaml
version: "3.8"

services:
  aria2:
    build: .
    container_name: aria2-docker
    ports:
      - "8080:8080"
    volumes:
      - ./downloads:/aria2/data
      - ./config:/aria2/conf
    environment:
      - PUID=1000
      - PGID=1000
    restart: unless-stopped
```

启动服务：

```bash
docker-compose up -d
```

### Nginx 代理配置

如果需要通过 Nginx 反向代理访问，可以使用以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_read_timeout 86400;
    }
}
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `PUID` | 运行用户的 UID，用于文件权限管理 | `0` (root) |
| `PGID` | 运行用户的 GID，用于文件权限管理 | `0` (root) |
| `ARIA2RPCPORT` | Aria2 RPC 端口 | `8080` |

### 数据卷

| 容器路径 | 说明 |
|----------|------|
| `/aria2/data` | 下载文件存储目录 |
| `/aria2/conf` | Aria2 配置文件目录 |

### 用户权限

为了避免文件权限问题，建议设置 `PUID` 和 `PGID` 环境变量：

1. 查看当前用户的 UID 和 GID：
```bash
id $(whoami)
```

2. 在运行容器时设置对应的环境变量：
```bash
docker run -d \
    -e PUID=1000 \
    -e PGID=1000 \
    ...
```

## 使用说明

1. 启动容器后，访问 `http://localhost:8080` 打开 YAAW Web 界面
2. 在界面中可以添加下载任务，支持：
   - HTTP/HTTPS 链接
   - FTP 链接
   - BitTorrent 种子文件
   - Magnet 磁力链接
3. 下载的文件将保存在 `/aria2/data` 目录中
4. 可以通过修改 `/aria2/conf/aria2.conf` 文件来自定义 Aria2 配置

## 构建镜像

克隆项目并构建：

```bash
git clone <repository-url>
cd aria2-docker
docker build -t aria2-docker .
```

## 许可证

本项目基于开源许可证发布，具体请查看项目中的 LICENSE 文件。

## 相关项目

- [Aria2](https://github.com/aria2/aria2) - 多协议下载工具
- [YAAW](https://github.com/binux/yaaw) - Aria2 Web 界面
- [Caddy](https://caddyserver.com/) - 现代化 Web 服务器
