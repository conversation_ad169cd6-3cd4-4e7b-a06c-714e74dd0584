import { OperationLog, OperationLogQuery, OperationLogResponse } from './types';

// 模拟数据
const mockLogs: OperationLog[] = Array.from({ length: 87 }).map((_, i) => ({
  id: i + 1,
  gid: `gid-${i + 1}`,
  file_count_num: 1,
  source_file: `/aria2/ssd/file${i + 1}.txt`,
  target_file: `/aria2/hdd/file${i + 1}.txt`,
  timestamp: new Date(Date.now() - i * 3600 * 1000).toISOString(),
  status: i % 5 === 0 ? 'failed' : 'success',
  error_message: i % 5 === 0 ? '移动失败，磁盘空间不足' : '',
}));

export async function fetchOperationLogs(query: OperationLogQuery): Promise<OperationLogResponse> {
  // 模拟网络延迟
  await new Promise((r) => setTimeout(r, 400));
  let filtered = mockLogs;
  if (query.status && query.status !== 'all') {
    filtered = filtered.filter((log) => log.status === query.status);
  }
  if (query.keyword) {
    filtered = filtered.filter(
      (log) => log.source_file.includes(query.keyword!) || log.target_file.includes(query.keyword!)
    );
  }
  if (query.dateRange && query.dateRange[0] && query.dateRange[1]) {
    filtered = filtered.filter((log) => {
      return log.timestamp >= query.dateRange![0] && log.timestamp <= query.dateRange![1];
    });
  }
  const total = filtered.length;
  const start = (query.page - 1) * query.pageSize;
  const end = start + query.pageSize;
  return {
    total,
    records: filtered.slice(start, end),
  };
}

export async function exportLogsToCSV(records: OperationLog[]): Promise<string> {
  const header = '时间戳,源文件,目标文件,状态,错误信息';
  const rows = records.map(
    (log) =>
      `"${log.timestamp}","${log.source_file}","${log.target_file}","${log.status === 'success' ? '成功' : '失败'}","${log.error_message.replace(/"/g, '""')}"`
  );
  return [header, ...rows].join('\n');
}
