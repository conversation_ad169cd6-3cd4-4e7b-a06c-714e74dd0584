<template>
  <div class="p-4 max-w-5xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">Aria2 操作记录</h1>
    <LogFilter
      :status="filter.status"
      :keyword="filter.keyword"
      :dateRange="filter.dateRange"
      @search="onFilterChange"
    />
    <div class="flex justify-between items-center mb-2">
      <div class="text-sm text-gray-500">共 {{ total }} 条记录</div>
      <button class="btn btn-outline btn-sm" @click="onExport" :disabled="loading">导出 CSV</button>
    </div>
    <div v-if="loading" class="text-center py-8 text-gray-400">加载中...</div>
    <div v-else>
      <table class="min-w-full table-auto border rounded shadow-sm bg-white">
        <thead>
          <tr class="bg-gray-100 text-xs md:text-sm">
            <th class="p-2">时间戳</th>
            <th class="p-2">源文件</th>
            <th class="p-2">目标文件</th>
            <th class="p-2">状态</th>
            <th class="p-2">错误信息</th>
          </tr>
        </thead>
        <tbody>
          <LogItem v-for="log in logs" :key="log.id" :log="log" />
        </tbody>
      </table>
      <div v-if="logs.length === 0" class="text-center text-gray-400 py-8">暂无操作记录</div>
      <div class="flex justify-center mt-4" v-if="total > pageSize">
        <button class="btn btn-sm mx-1" :disabled="page === 1" @click="page--">上一页</button>
        <span class="px-2 text-sm">第 {{ page }} / {{ totalPages }} 页</span>
        <button class="btn btn-sm mx-1" :disabled="page === totalPages" @click="page++">下一页</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import LogFilter from './components/LogFilter.vue';
import LogItem from './components/LogItem.vue';
import { fetchOperationLogs, exportLogsToCSV } from './api';
import type { OperationLog, OperationLogQuery } from './types';

const logs = ref<OperationLog[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = 20;
const loading = ref(false);
const filter = ref({
  status: 'all',
  keyword: '',
  dateRange: ['', ''],
});

const totalPages = computed(() => Math.ceil(total.value / pageSize));

async function loadLogs() {
  loading.value = true;
  const query: OperationLogQuery = {
    page: page.value,
    pageSize,
    status: filter.value.status as any,
    keyword: filter.value.keyword,
    dateRange: filter.value.dateRange as [string, string],
  };
  const res = await fetchOperationLogs(query);
  logs.value = res.records;
  total.value = res.total;
  loading.value = false;
}

function onFilterChange(newFilter: any) {
  filter.value = { ...filter.value, ...newFilter };
  page.value = 1;
  loadLogs();
}

async function onExport() {
  loading.value = true;
  // 导出全部数据
  const res = await fetchOperationLogs({ ...filter.value, page: 1, pageSize: 9999 });
  const csv = await exportLogsToCSV(res.records);
  const blob = new Blob([csv], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'operation_logs.csv';
  a.click();
  URL.revokeObjectURL(url);
  loading.value = false;
}

watch([page, filter], loadLogs, { immediate: true });
</script>

<style scoped>
.table-auto th, .table-auto td {
  @apply px-2 py-1 border;
}
.btn {
  @apply px-3 py-1 rounded;
}
</style>
