package main

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// OperationLog 结构体
// 包含：ID、操作类型、文件路径、状态、创建时间、完成时间、错误信息等
// 操作类型暂定为 "move"，可扩展

type OperationLog struct {
	ID           int64  `json:"id"`
	Gid          string `json:"gid"`
	FileCountNum int    `json:"file_count_num"`
	SourceFile   string `json:"source_file"`
	TargetFile   string `json:"target_file"`
	Status       string `json:"status"`
	ErrorMessage string `json:"error_message"`
	CreatedAt    string `json:"created_at"`
	FinishedAt   string `json:"finished_at"`
	OpType       string `json:"op_type"`
}

type PageResult struct {
	Total   int            `json:"total"`
	Page    int            `json:"page"`
	Limit   int            `json:"limit"`
	Records []OperationLog `json:"records"`
}

type DeleteRequest struct {
	IDs []int64 `json:"ids"`
}

type DeleteResponse struct {
	Deleted int `json:"deleted"`
}

// 查询操作记录（分页、筛选、搜索）
func getOperationLogs(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	keyword := c.QueryParam("keyword")
	startTime := c.QueryParam("start_time")
	endTime := c.QueryParam("end_time")

	var where []string
	var args []interface{}

	if keyword != "" {
		where = append(where, "(source_file LIKE ? OR target_file LIKE ? OR status LIKE ? OR error_message LIKE ?)")
		kw := "%" + keyword + "%"
		args = append(args, kw, kw, kw, kw)
	}
	if startTime != "" {
		where = append(where, "timestamp >= ?")
		args = append(args, startTime)
	}
	if endTime != "" {
		where = append(where, "timestamp <= ?")
		args = append(args, endTime)
	}

	whereSQL := ""
	if len(where) > 0 {
		whereSQL = "WHERE " + strings.Join(where, " AND ")
	}

	countSQL := "SELECT COUNT(*) FROM file_operations " + whereSQL
	row := db.QueryRow(countSQL, args...)
	var total int
	err := row.Scan(&total)
	if err != nil {
		log.Printf("[API] 查询总数失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db count error"})
	}

	offset := (page - 1) * limit
	querySQL := `SELECT id, gid, file_count_num, source_file, target_file, status, error_message, timestamp FROM file_operations ` + whereSQL + ` ORDER BY id DESC LIMIT ? OFFSET ?`
	args2 := append(args, limit, offset)
	rows, err := db.Query(querySQL, args2...)
	if err != nil {
		log.Printf("[API] 查询记录失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db query error"})
	}
	defer rows.Close()

	var records []OperationLog
	for rows.Next() {
		var logItem OperationLog
		var ts string
		err := rows.Scan(&logItem.ID, &logItem.Gid, &logItem.FileCountNum, &logItem.SourceFile, &logItem.TargetFile, &logItem.Status, &logItem.ErrorMessage, &ts)
		if err != nil {
			log.Printf("[API] 记录解析失败: %v", err)
			continue
		}
		logItem.CreatedAt = ts
		logItem.FinishedAt = ts
		logItem.OpType = "move"
		records = append(records, logItem)
	}

	return c.JSON(http.StatusOK, PageResult{
		Total:   total,
		Page:    page,
		Limit:   limit,
		Records: records,
	})
}

// 单个删除
func deleteOperationLog(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid id"})
	}
	res, err := db.Exec("DELETE FROM file_operations WHERE id = ?", id)
	if err != nil {
		log.Printf("[API] 删除记录失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db delete error"})
	}
	n, _ := res.RowsAffected()
	return c.JSON(http.StatusOK, DeleteResponse{Deleted: int(n)})
}

// 批量删除
func batchDeleteOperationLogs(c echo.Context) error {
	var req DeleteRequest
	if err := json.NewDecoder(c.Request().Body).Decode(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid body"})
	}
	if len(req.IDs) == 0 {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "empty ids"})
	}
	placeholders := make([]string, len(req.IDs))
	args := make([]interface{}, len(req.IDs))
	for i, id := range req.IDs {
		placeholders[i] = "?"
		args[i] = id
	}
	query := "DELETE FROM file_operations WHERE id IN (" + strings.Join(placeholders, ",") + ")"
	res, err := db.Exec(query, args...)
	if err != nil {
		log.Printf("[API] 批量删除失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db batch delete error"})
	}
	n, _ := res.RowsAffected()
	return c.JSON(http.StatusOK, DeleteResponse{Deleted: int(n)})
}

func main() {
	// ...原有 main 逻辑...

	// 启动 API 服务
	e := echo.New()
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	e.GET("/api/operations", getOperationLogs)
	e.DELETE("/api/operations/:id", deleteOperationLog)
	e.DELETE("/api/operations", batchDeleteOperationLogs)

	log.Println("[API] 启动 Aria2 操作记录 API 服务 :8081")
	e.Logger.Fatal(e.Start(":8081"))
}
