package main

import (
	"encoding/json"
	"log"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// OperationLog 结构体已在 db.go 定义

type PageResult struct {
	Total   int            `json:"total"`
	Page    int            `json:"page"`
	Limit   int            `json:"limit"`
	Records []OperationLog `json:"records"`
}

type DeleteRequest struct {
	IDs []int64 `json:"ids"`
}

type DeleteResponse struct {
	Deleted int `json:"deleted"`
}

type MoveFileRequest struct {
	FileCountNum  int    `json:"file_count_num"`
	FirstFilePath string `json:"first_file_path"`
	SourceFolder  string `json:"source_folder"`
	TargetFolder  string `json:"target_folder"`
}

type MoveFileResponse struct {
	Status     string `json:"status"`
	ErrorMsg   string `json:"error_message,omitempty"`
	SourceFile string `json:"source_file,omitempty"`
	TargetFile string `json:"target_file,omitempty"`
}

// 查询操作记录（分页、筛选、搜索）
func getOperationLogs(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	keyword := c.QueryParam("keyword")
	startTime := c.QueryParam("start_time")
	endTime := c.QueryParam("end_time")

	var where []string
	var args []interface{}

	if keyword != "" {
		where = append(where, "(source_file LIKE ? OR target_file LIKE ? OR status LIKE ? OR error_message LIKE ?)")
		kw := "%" + keyword + "%"
		args = append(args, kw, kw, kw, kw)
	}
	if startTime != "" {
		where = append(where, "timestamp >= ?")
		args = append(args, startTime)
	}
	if endTime != "" {
		where = append(where, "timestamp <= ?")
		args = append(args, endTime)
	}

	whereSQL := ""
	if len(where) > 0 {
		whereSQL = "WHERE " + strings.Join(where, " AND ")
	}

	countSQL := "SELECT COUNT(*) FROM file_operations " + whereSQL
	row := db.QueryRow(countSQL, args...)
	var total int
	err := row.Scan(&total)
	if err != nil {
		log.Printf("[API] 查询总数失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db count error"})
	}

	offset := (page - 1) * limit
	querySQL := `SELECT id, gid, file_count_num, source_file, target_file, status, error_message, timestamp FROM file_operations ` + whereSQL + ` ORDER BY id DESC LIMIT ? OFFSET ?`
	args2 := append(args, limit, offset)
	rows, err := db.Query(querySQL, args2...)
	if err != nil {
		log.Printf("[API] 查询记录失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db query error"})
	}
	defer rows.Close()

	var records []OperationLog
	for rows.Next() {
		var logItem OperationLog
		var ts string
		err := rows.Scan(&logItem.ID, &logItem.Gid, &logItem.FileCountNum, &logItem.SourceFile, &logItem.TargetFile, &logItem.Status, &logItem.ErrorMessage, &ts)
		if err != nil {
			log.Printf("[API] 记录解析失败: %v", err)
			continue
		}
		logItem.CreatedAt = ts
		logItem.FinishedAt = ts
		logItem.OpType = "move"
		records = append(records, logItem)
	}

	return c.JSON(http.StatusOK, PageResult{
		Total:   total,
		Page:    page,
		Limit:   limit,
		Records: records,
	})
}

// 单个删除
func deleteOperationLog(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid id"})
	}
	res, err := db.Exec("DELETE FROM file_operations WHERE id = ?", id)
	if err != nil {
		log.Printf("[API] 删除记录失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db delete error"})
	}
	n, _ := res.RowsAffected()
	return c.JSON(http.StatusOK, DeleteResponse{Deleted: int(n)})
}

// 批量删除
func batchDeleteOperationLogs(c echo.Context) error {
	var req DeleteRequest
	if err := json.NewDecoder(c.Request().Body).Decode(&req); err != nil {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid body"})
	}
	if len(req.IDs) == 0 {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "empty ids"})
	}
	placeholders := make([]string, len(req.IDs))
	args := make([]interface{}, len(req.IDs))
	for i, id := range req.IDs {
		placeholders[i] = "?"
		args[i] = id
	}
	query := "DELETE FROM file_operations WHERE id IN (" + strings.Join(placeholders, ",") + ")"
	res, err := db.Exec(query, args...)
	if err != nil {
		log.Printf("[API] 批量删除失败: %v", err)
		return c.JSON(http.StatusInternalServerError, echo.Map{"error": "db batch delete error"})
	}
	n, _ := res.RowsAffected()
	return c.JSON(http.StatusOK, DeleteResponse{Deleted: int(n)})
}

func moveFileHandler(c echo.Context) error {
	var req MoveFileRequest
	if err := c.Bind(&req); err != nil {
		log.Printf("[API] 参数解析失败: %v", err)
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "invalid request body"})
	}
	if req.FileCountNum <= 0 || req.FirstFilePath == "" || req.SourceFolder == "" || req.TargetFolder == "" {
		return c.JSON(http.StatusBadRequest, echo.Map{"error": "missing or invalid parameters"})
	}

	file_paths := strings.Split(req.FirstFilePath, "/")
	if len(file_paths) < 4 {
		log.Println("first_file_path not right, return")
		return c.JSON(http.StatusBadRequest, MoveFileResponse{Status: "failed", ErrorMsg: "first_file_path not right"})
	}
	source_file := req.SourceFolder + file_paths[3]
	target_file := req.TargetFolder + file_paths[3]
	log.Println("source_file: " + source_file)

	opStatus := "success"
	opErrMsg := ""

	if source_file != "" {
		log.Println("start move form " + source_file + " to " + target_file)
		_, err := os.Stat(source_file)
		if err != nil {
			log.Println("source_file:"+source_file+"not exist", err)
			opStatus = "failed"
			opErrMsg = err.Error()
			_ = InsertFileOperation(db, "", req.FileCountNum, source_file, target_file, opStatus, opErrMsg)
			return c.JSON(http.StatusInternalServerError, MoveFileResponse{Status: opStatus, ErrorMsg: opErrMsg, SourceFile: source_file, TargetFile: target_file})
		}
		err = os.Rename(source_file, target_file)
		if err != nil {
			log.Println("move error, try copy", err)
			err := MoveFileExec(source_file, target_file)
			if err != nil {
				log.Println("move file error", err)
				opStatus = "failed"
				opErrMsg = err.Error()
				_ = InsertFileOperation(db, "", req.FileCountNum, source_file, target_file, opStatus, opErrMsg)
				return c.JSON(http.StatusInternalServerError, MoveFileResponse{Status: opStatus, ErrorMsg: opErrMsg, SourceFile: source_file, TargetFile: target_file})
			}
		}
		log.Println("move finish")
		log.Println("chomd start")
		cmd := exec.Command("chmod", "-R", "o+w", target_file)
		err = cmd.Run()
		if err != nil {
			log.Println("chmod fail", err)
			opStatus = "failed"
			opErrMsg = err.Error()
			_ = InsertFileOperation(db, "", req.FileCountNum, source_file, target_file, opStatus, opErrMsg)
			return c.JSON(http.StatusInternalServerError, MoveFileResponse{Status: opStatus, ErrorMsg: opErrMsg, SourceFile: source_file, TargetFile: target_file})
		}
		log.Println("chmod finish")
	} else {
		log.Println(source_file + " is empty")
	}
	_ = InsertFileOperation(db, "", req.FileCountNum, source_file, target_file, opStatus, opErrMsg)
	return c.JSON(http.StatusOK, MoveFileResponse{Status: opStatus, SourceFile: source_file, TargetFile: target_file})
}

func main() {
	// ...原有 main 逻辑...

	// 启动 API 服务
	e := echo.New()
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	e.GET("/api/operations", getOperationLogs)
	e.DELETE("/api/operations/:id", deleteOperationLog)
	e.DELETE("/api/operations", batchDeleteOperationLogs)
	// 新增文件移动API
	e.POST("/api/move-file", moveFileHandler)

	log.Println("[API] 启动 Aria2 操作记录 API 服务 :8081")
	e.Logger.Fatal(e.Start(":8081"))
}
