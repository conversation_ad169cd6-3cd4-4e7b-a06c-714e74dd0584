package main

import (
	"bytes"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// captureLogOutput 捕获日志输出用于测试
func captureLogOutput(f func()) string {
	var buf bytes.Buffer
	log.SetOutput(&buf)
	defer log.SetOutput(os.Stderr)
	f()
	return buf.String()
}

// createTempDir 创建临时目录用于测试
func createTempDir(t *testing.T, name string) string {
	dir, err := os.MkdirTemp("", name)
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	return dir
}

// createTempFile 在指定目录创建临时文件
func createTempFile(t *testing.T, dir, name, content string) string {
	filePath := filepath.Join(dir, name)
	err := os.WriteFile(filePath, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	return filePath
}

// TestProcessFileMove_ArgumentValidation 测试参数验证
func TestProcessFileMove_ArgumentValidation(t *testing.T) {
	tests := []struct {
		name     string
		args     []string
		expected string
	}{
		{
			name:     "参数数量不足_0个参数",
			args:     []string{},
			expected: "args is less than 2",
		},
		{
			name:     "参数数量不足_1个参数",
			args:     []string{"gid"},
			expected: "args is less than 2",
		},
		{
			name:     "参数数量不足_2个参数",
			args:     []string{"gid", "count"},
			expected: "args is less than 2",
		},
		{
			name:     "file_count为空字符串",
			args:     []string{"gid", "", "/aria2/ssd/test/file.txt"},
			expected: "file_count is empty, return",
		},
		{
			name:     "first_file_path为空字符串",
			args:     []string{"gid", "1", ""},
			expected: "first_file_path is empty, return",
		},
		{
			name:     "file_count不是有效数字",
			args:     []string{"gid", "abc", "/aria2/ssd/test/file.txt"},
			expected: "file_count is not i:",
		},
		{
			name:     "file_count为0",
			args:     []string{"gid", "0", "/aria2/ssd/test/file.txt"},
			expected: "file_count_num <= 0, retrun",
		},
		{
			name:     "file_count为负数",
			args:     []string{"gid", "-1", "/aria2/ssd/test/file.txt"},
			expected: "file_count_num <= 0, retrun",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := captureLogOutput(func() {
				ProcessFileMove(tt.args, "/tmp/source", "/tmp/target")
			})

			if !strings.Contains(output, tt.expected) {
				t.Errorf("Expected log to contain '%s', got: %s", tt.expected, output)
			}
		})
	}
}

// TestProcessFileMove_PathParsing 测试文件路径解析
func TestProcessFileMove_PathParsing(t *testing.T) {
	tests := []struct {
		name     string
		args     []string
		expected string
	}{
		{
			name:     "路径格式不正确_分隔符少于4个",
			args:     []string{"gid", "1", "/aria2/ssd"},
			expected: "first_file_path not right, return",
		},
		{
			name:     "路径格式不正确_只有3个分隔符",
			args:     []string{"gid", "1", "/aria2/ssd"},
			expected: "first_file_path not right, return",
		},
		{
			name:     "正常路径格式",
			args:     []string{"gid", "1", "/aria2/ssd/test/file.txt"},
			expected: "source_file: /tmp/source/test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := captureLogOutput(func() {
				ProcessFileMove(tt.args, "/tmp/source/", "/tmp/target/")
			})

			if !strings.Contains(output, tt.expected) {
				t.Errorf("Expected log to contain '%s', got: %s", tt.expected, output)
			}
		})
	}
}

// TestProcessFileMove_FileOperations 测试文件操作
func TestProcessFileMove_FileOperations(t *testing.T) {
	// 创建临时目录
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	// 创建测试文件
	testFile := createTempFile(t, sourceDir, "testfile.txt", "test content")

	t.Run("源文件存在时的移动操作", func(t *testing.T) {
		args := []string{"gid", "1", "/aria2/ssd/testfile.txt/file.txt"}

		output := captureLogOutput(func() {
			err := ProcessFileMove(args, sourceDir+"/", targetDir+"/")
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})

		expectedLogs := []string{
			"file_count: 1",
			"first_file_path: /aria2/ssd/testfile.txt/file.txt",
			"file_count_num is 1",
			"source_file: " + sourceDir + "/testfile.txt",
			"start move form",
			"move finish",
			"chomd start",
			"chmod finish",
		}

		for _, expected := range expectedLogs {
			if !strings.Contains(output, expected) {
				t.Errorf("Expected log to contain '%s', got: %s", expected, output)
			}
		}

		// 验证文件是否被移动
		targetFile := filepath.Join(targetDir, "testfile.txt")
		if _, err := os.Stat(targetFile); os.IsNotExist(err) {
			t.Errorf("File was not moved to target directory")
		}
		if _, err := os.Stat(testFile); !os.IsNotExist(err) {
			t.Errorf("File still exists in source directory")
		}
	})

	t.Run("源文件不存在时的错误处理", func(t *testing.T) {
		args := []string{"gid", "1", "/aria2/ssd/nonexistent/file.txt"}

		output := captureLogOutput(func() {
			err := ProcessFileMove(args, sourceDir+"/", targetDir+"/")
			if err == nil {
				t.Error("Expected error when source file doesn't exist")
			}
		})

		if !strings.Contains(output, "not exist") {
			t.Errorf("Expected log to contain 'not exist', got: %s", output)
		}
	})
}

// TestProcessFileMove_RenameFailure 测试 os.Rename 失败时回退到 MoveFileExec
func TestProcessFileMove_RenameFailure(t *testing.T) {
	// 创建临时目录
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	// 创建测试文件
	createTempFile(t, sourceDir, "testfile.txt", "test content")

	// 创建目标目录并设置为只读，这样 os.Rename 会失败
	os.Chmod(targetDir, 0555)       // 只读目录
	defer os.Chmod(targetDir, 0755) // 恢复权限以便清理

	args := []string{"gid", "1", "/aria2/ssd/testfile.txt/file.txt"}

	output := captureLogOutput(func() {
		ProcessFileMove(args, sourceDir+"/", targetDir+"/")
	})

	expectedLogs := []string{
		"move error, try copy",
		"run command: mv",
	}

	for _, expected := range expectedLogs {
		if !strings.Contains(output, expected) {
			t.Errorf("Expected log to contain '%s', got: %s", expected, output)
		}
	}
}

// TestMoveFileExec 测试 MoveFileExec 函数
func TestMoveFileExec(t *testing.T) {
	// 创建临时目录
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	t.Run("成功执行mv命令", func(t *testing.T) {
		// 创建测试文件
		sourceFile := createTempFile(t, sourceDir, "testfile.txt", "test content")
		targetFile := filepath.Join(targetDir, "testfile.txt")

		output := captureLogOutput(func() {
			err := MoveFileExec(sourceFile, targetFile)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})

		if !strings.Contains(output, "run command: mv") {
			t.Errorf("Expected log to contain 'run command: mv', got: %s", output)
		}

		// 验证文件是否被移动
		if _, err := os.Stat(targetFile); os.IsNotExist(err) {
			t.Errorf("File was not moved to target location")
		}
	})

	t.Run("mv命令执行失败", func(t *testing.T) {
		// 使用不存在的源文件
		sourceFile := filepath.Join(sourceDir, "nonexistent.txt")
		targetFile := filepath.Join(targetDir, "target.txt")

		output := captureLogOutput(func() {
			err := MoveFileExec(sourceFile, targetFile)
			if err == nil {
				t.Error("Expected error when moving nonexistent file")
			}
		})

		if !strings.Contains(output, "run command: mv") {
			t.Errorf("Expected log to contain 'run command: mv', got: %s", output)
		}
	})
}

// TestLogOutput 测试日志输出格式
func TestLogOutput(t *testing.T) {
	// 保存原始的日志输出
	originalOutput := log.Writer()
	defer log.SetOutput(originalOutput)

	var buf bytes.Buffer
	log.SetOutput(&buf)

	// 测试日志是否包含时间戳
	args := []string{"gid", "1", "/aria2/ssd/test/file.txt"}
	ProcessFileMove(args, "/tmp/source/", "/tmp/target/")

	output := buf.String()

	// 检查日志是否包含时间戳格式 (YYYY/MM/DD HH:MM:SS)
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		if line != "" {
			// 每行日志应该以时间戳开始
			if !strings.Contains(line, "/") || !strings.Contains(line, ":") {
				t.Errorf("Log line should contain timestamp, got: %s", line)
			}
		}
	}

	// 验证特定的日志消息
	expectedMessages := []string{
		"file_count: 1",
		"first_file_path: /aria2/ssd/test/file.txt",
		"file_count_num is 1",
	}

	for _, expected := range expectedMessages {
		if !strings.Contains(output, expected) {
			t.Errorf("Expected log to contain '%s', got: %s", expected, output)
		}
	}
}

// TestChmodFailure 测试 chmod 操作失败的情况
func TestChmodFailure(t *testing.T) {
	// 创建临时目录
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	// 创建测试文件
	createTempFile(t, sourceDir, "testfile.txt", "test content")

	args := []string{"gid", "1", "/aria2/ssd/testfile.txt/file.txt"}

	// 模拟 chmod 失败的情况（通过使用无效的目标路径）
	output := captureLogOutput(func() {
		// 使用一个不存在的目标目录来触发 chmod 失败
		err := ProcessFileMove(args, sourceDir+"/", "/invalid/path/")
		if err == nil {
			t.Error("Expected error when chmod fails")
		}
	})

	// 由于目标路径无效，应该在移动文件时就失败
	if !strings.Contains(output, "source_file:") {
		t.Errorf("Expected log to contain source file info, got: %s", output)
	}
}

// TestIntegration 集成测试
func TestIntegration(t *testing.T) {
	// 创建临时目录结构
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	// 创建测试文件
	testFileName := "integration_test.txt"
	sourceFile := createTempFile(t, sourceDir, testFileName, "integration test content")

	args := []string{"gid", "1", "/aria2/ssd/" + testFileName + "/file.txt"}

	output := captureLogOutput(func() {
		err := ProcessFileMove(args, sourceDir+"/", targetDir+"/")
		if err != nil {
			t.Errorf("Integration test failed with error: %v", err)
		}
	})

	// 验证完整的处理流程
	expectedFlow := []string{
		"file_count: 1",
		"first_file_path:",
		"file_count_num is 1",
		"source_file:",
		"start move form",
		"move finish",
		"chomd start",
		"chmod finish",
	}

	for _, expected := range expectedFlow {
		if !strings.Contains(output, expected) {
			t.Errorf("Integration test: expected log to contain '%s', got: %s", expected, output)
		}
	}

	// 验证文件确实被移动了
	targetFile := filepath.Join(targetDir, testFileName)
	if _, err := os.Stat(targetFile); os.IsNotExist(err) {
		t.Error("Integration test: file was not moved to target directory")
	}
	if _, err := os.Stat(sourceFile); !os.IsNotExist(err) {
		t.Error("Integration test: file still exists in source directory")
	}

	// 验证文件内容
	content, err := os.ReadFile(targetFile)
	if err != nil {
		t.Errorf("Integration test: failed to read target file: %v", err)
	}
	if string(content) != "integration test content" {
		t.Errorf("Integration test: file content mismatch, got: %s", string(content))
	}
}
