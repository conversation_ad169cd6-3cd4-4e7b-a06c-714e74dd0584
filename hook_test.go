package main

import (
	"bytes"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestInitDB_AndInsertFileOperation 测试数据库初始化和插入操作
func TestInitDB_AndInsertFileOperation(t *testing.T) {
	tmpDB := filepath.Join(os.TempDir(), "test_aria2_hook.db")
	defer os.Remove(tmpDB)

	testDB, err := InitDB(tmpDB)
	if err != nil {
		t.Fatalf("InitDB failed: %v", err)
	}
	defer testDB.Close()

	// 插入一条记录
	stmt := `INSERT INTO file_operations (gid, file_count_num, source_file, target_file, status, error_message) VALUES (?, ?, ?, ?, ?, ?)`
	_, err = testDB.Exec(stmt, "gid123", 2, "/src/file", "/dst/file", "success", "")
	if err != nil {
		t.Fatalf("Insert failed: %v", err)
	}

	// 查询验证
	var gid, status string
	err = testDB.QueryRow("SELECT gid, status FROM file_operations WHERE gid=?", "gid123").Scan(&gid, &status)
	if err != nil {
		t.Fatalf("Query failed: %v", err)
	}
	if gid != "gid123" || status != "success" {
		t.Errorf("Unexpected db record: gid=%s, status=%s", gid, status)
	}

	// 再次调用 InitDB，确保不会因表已存在而报错
	testDB2, err := InitDB(tmpDB)
	if err != nil {
		t.Fatalf("InitDB failed on existing table: %v", err)
	}
	testDB2.Close()
}

// TestParseAndValidateArgs 参数解析与验证的单元测试
func TestParseAndValidateArgs(t *testing.T) {
	tests := []struct {
		name      string
		args      []string
		wantGid   string
		wantCount int
		wantPath  string
		wantErr   string
	}{
		{"正常参数", []string{"gid", "1", "/aria2/ssd/file.txt"}, "gid", 1, "/aria2/ssd/file.txt", ""},
		{"参数数量不足0", []string{}, "", 0, "", "args is less than 2"},
		{"参数数量不足1", []string{"gid"}, "", 0, "", "args is less than 2"},
		{"参数数量不足2", []string{"gid", "1"}, "", 0, "", "args is less than 2"},
		{"file_count为空", []string{"gid", "", "/aria2/ssd/file.txt"}, "gid", 0, "/aria2/ssd/file.txt", "file_count is empty"},
		{"first_file_path为空", []string{"gid", "1", ""}, "gid", 0, "", "first_file_path is empty"},
		{"file_count不是数字", []string{"gid", "abc", "/aria2/ssd/file.txt"}, "gid", 0, "/aria2/ssd/file.txt", "file_count is not i:"},
		{"file_count为0", []string{"gid", "0", "/aria2/ssd/file.txt"}, "gid", 0, "/aria2/ssd/file.txt", "file_count_num <= 0"},
		{"file_count为负数", []string{"gid", "-2", "/aria2/ssd/file.txt"}, "gid", -2, "/aria2/ssd/file.txt", "file_count_num <= 0"},
		{"边界值1", []string{"gid", "1", "/aria2/ssd/file.txt"}, "gid", 1, "/aria2/ssd/file.txt", ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gid, count, path, err := parseAndValidateArgs(tt.args)
			if gid != tt.wantGid {
				t.Errorf("gid want %q, got %q", tt.wantGid, gid)
			}
			if count != tt.wantCount {
				t.Errorf("fileCount want %d, got %d", tt.wantCount, count)
			}
			if path != tt.wantPath {
				t.Errorf("firstFilePath want %q, got %q", tt.wantPath, path)
			}
			if tt.wantErr == "" && err != nil {
				t.Errorf("want no error, got %v", err)
			}
			if tt.wantErr != "" {
				if err == nil {
					t.Errorf("want error containing %q, got nil", tt.wantErr)
				} else if !strings.Contains(err.Error(), tt.wantErr) {
					t.Errorf("want error containing %q, got %v", tt.wantErr, err)
				}
			}
		})
	}
}

// captureLogOutput 捕获日志输出用于测试
func captureLogOutput(f func()) string {
	var buf bytes.Buffer
	log.SetOutput(&buf)
	defer log.SetOutput(os.Stderr)
	f()
	return buf.String()
}

// createTempDir 创建临时目录用于测试
func createTempDir(t *testing.T, name string) string {
	dir, err := os.MkdirTemp("", name)
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	return dir
}

// createTempFile 在指定目录创建临时文件
func createTempFile(t *testing.T, dir, name, content string) string {
	filePath := filepath.Join(dir, name)
	err := os.WriteFile(filePath, []byte(content), 0644)
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	return filePath
}

// TestProcessFileMove_PathParsing 测试文件路径解析
func TestProcessFileMove_PathParsing(t *testing.T) {
	tests := []struct {
		name     string
		args     []string
		expected string
	}{
		{
			name:     "路径格式不正确_分隔符少于4个",
			args:     []string{"gid", "1", "/aria2/ssd"},
			expected: "first_file_path not right, return",
		},
		{
			name:     "路径格式不正确_只有3个分隔符",
			args:     []string{"gid", "1", "/aria2/ssd"},
			expected: "first_file_path not right, return",
		},
		{
			name:     "正常路径格式",
			args:     []string{"gid", "1", "/aria2/ssd/test/file.txt"},
			expected: "source_file: /tmp/source/test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := captureLogOutput(func() {
				_, file_count_num, first_file_path, _ := parseAndValidateArgs(tt.args)
				ProcessFileMove(file_count_num, first_file_path, "/tmp/source/", "/tmp/target/")
			})

			if !strings.Contains(output, tt.expected) {
				t.Errorf("Expected log to contain '%s', got: %s", tt.expected, output)
			}
		})
	}
}

// TestProcessFileMove_FileOperations 测试文件操作
func TestProcessFileMove_FileOperations(t *testing.T) {
	// 创建临时目录
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	// 创建测试文件
	testFile := createTempFile(t, sourceDir, "testfile.txt", "test content")

	t.Run("源文件存在时的移动操作", func(t *testing.T) {
		args := []string{"gid", "1", "/aria2/ssd/testfile.txt/file.txt"}

		output := captureLogOutput(func() {
			_, file_count_num, first_file_path, _ := parseAndValidateArgs(args)
			err := ProcessFileMove(file_count_num, first_file_path, sourceDir+"/", targetDir+"/")
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})

		expectedLogs := []string{
			"file_paths has 5 /",
			"start move form",
			"move finish",
			"chomd start",
			"chmod finish",
		}

		for _, expected := range expectedLogs {
			if !strings.Contains(output, expected) {
				t.Errorf("Expected log to contain '%s', got: %s", expected, output)
			}
		}

		// 验证文件是否被移动
		targetFile := filepath.Join(targetDir, "testfile.txt")
		if _, err := os.Stat(targetFile); os.IsNotExist(err) {
			t.Errorf("File was not moved to target directory")
		}
		if _, err := os.Stat(testFile); !os.IsNotExist(err) {
			t.Errorf("File still exists in source directory")
		}
	})

	t.Run("源文件不存在时的错误处理", func(t *testing.T) {
		args := []string{"gid", "1", "/aria2/ssd/nonexistent/file.txt"}

		output := captureLogOutput(func() {
			_, file_count_num, first_file_path, _ := parseAndValidateArgs(args)
			err := ProcessFileMove(file_count_num, first_file_path, sourceDir+"/", targetDir+"/")
			if err == nil {
				t.Error("Expected error when source file doesn't exist")
			}
		})

		if !strings.Contains(output, "not exist") {
			t.Errorf("Expected log to contain 'not exist', got: %s", output)
		}
	})
}

// TestMoveFileExec 测试 MoveFileExec 函数
func TestMoveFileExec(t *testing.T) {
	// 创建临时目录
	sourceDir := createTempDir(t, "source")
	targetDir := createTempDir(t, "target")
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	t.Run("成功执行mv命令", func(t *testing.T) {
		// 创建测试文件
		sourceFile := createTempFile(t, sourceDir, "testfile.txt", "test content")
		targetFile := filepath.Join(targetDir, "testfile.txt")

		output := captureLogOutput(func() {
			err := MoveFileExec(sourceFile, targetFile)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})

		if !strings.Contains(output, "run command: mv") {
			t.Errorf("Expected log to contain 'run command: mv', got: %s", output)
		}

		// 验证文件是否被移动
		if _, err := os.Stat(targetFile); os.IsNotExist(err) {
			t.Errorf("File was not moved to target location")
		}
	})

	t.Run("mv命令执行失败", func(t *testing.T) {
		// 使用不存在的源文件
		sourceFile := filepath.Join(sourceDir, "nonexistent.txt")
		targetFile := filepath.Join(targetDir, "target.txt")

		output := captureLogOutput(func() {
			err := MoveFileExec(sourceFile, targetFile)
			if err == nil {
				t.Error("Expected error when moving nonexistent file")
			}
		})

		if !strings.Contains(output, "run command: mv") {
			t.Errorf("Expected log to contain 'run command: mv', got: %s", output)
		}
	})
}

// TestProcessFileMove_Success 测试文件移动成功的情况
func TestProcessFileMove_Success(t *testing.T) {
	sourceDir, err := os.MkdirTemp("", "source")
	if err != nil {
		t.Fatalf("failed to create temp source dir: %v", err)
	}
	targetDir, err := os.MkdirTemp("", "target")
	if err != nil {
		os.RemoveAll(sourceDir)
		t.Fatalf("failed to create temp target dir: %v", err)
	}
	defer os.RemoveAll(sourceDir)
	defer os.RemoveAll(targetDir)

	testFileName := "testfile.txt"
	testFilePath := sourceDir + "/" + testFileName
	testContent := []byte("hello world\n测试内容")
	err = os.WriteFile(testFilePath, testContent, 0644)
	if err != nil {
		t.Fatalf("failed to write test file: %v", err)
	}

	first_file_path := "/aria2/ssd/" + testFileName + "/file.txt"
	file_count_num := 1

	var logBuf strings.Builder
	log.SetOutput(&logBuf)
	defer log.SetOutput(os.Stderr)

	err = ProcessFileMove(file_count_num, first_file_path, sourceDir+"/", targetDir+"/")
	if err != nil {
		t.Fatalf("ProcessFileMove failed: %v", err)
	}

	// 检查目标文件是否存在且内容一致
	targetFilePath := targetDir + "/" + testFileName
	data, err := os.ReadFile(targetFilePath)
	if err != nil {
		t.Fatalf("target file not found: %v", err)
	}
	if string(data) != string(testContent) {
		t.Errorf("file content mismatch, want %q, got %q", string(testContent), string(data))
	}

	// 检查源文件已被删除
	if _, err := os.Stat(testFilePath); !os.IsNotExist(err) {
		t.Errorf("source file should be removed after move")
	}

	// 检查日志输出
	logStr := logBuf.String()
	if !strings.Contains(logStr, "move finish") {
		t.Errorf("log should contain 'move finish', got: %s", logStr)
	}
	if !strings.Contains(logStr, "chmod finish") {
		t.Errorf("log should contain 'chmod finish', got: %s", logStr)
	}
}
